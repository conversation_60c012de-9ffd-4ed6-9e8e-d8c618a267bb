{"root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "prettier"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "eslint-config-prettier", "prettier"], "rules": {"@typescript-eslint/no-explicit-any": "off", "prettier/prettier": ["warn", {"arrowParens": "always", "semi": true, "trailingComma": "all", "tabWidth": 2, "endOfLine": "auto", "useTabs": false, "singleQuote": false, "printWidth": 120, "jsxSingleQuote": false, "singleAttributePerLine": true}]}}