import { Router } from "express";
import {
  checkBookmarkController,
  createBookmarkController,
  getBookmarksController,
  removeBookmarkController,
} from "~/controllers/bookmarks.controllers";
import { accessTokenValidator, verifiedUserValidator } from "~/middlewares/users.middlewares";
import { wrapRequestHandler } from "~/utils/requestHandlers";

const bookmarkRouter = Router();

bookmarkRouter.get("/", accessTokenValidator, verifiedUserValidator, wrapRequestHandler(getBookmarksController));
bookmarkRouter.get(
  "/tweets/:tweet_id",
  accessTokenValidator,
  verifiedUserValidator,
  wrap<PERSON><PERSON><PERSON><PERSON>and<PERSON>(checkBookmarkController),
);
bookmarkRouter.post("/", accessTokenValidator, verifiedUserValidator, wrapRequestHandler(createBookmarkController));
bookmarkRouter.delete(
  "/tweets/:tweet_id",
  accessTokenValidator,
  verified<PERSON>ser<PERSON><PERSON><PERSON>tor,
  wrapRequestHand<PERSON>(removeBookmarkController),
);

export default bookmarkRouter;
