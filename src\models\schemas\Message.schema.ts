import { ObjectId } from "mongodb";

type TMessage = {
  _id?: ObjectId;
  sender_id: ObjectId;
  receiver_id: ObjectId;
  content: string;
  created_at?: Date;
};

// Yeah yeah, I know that I should create a Conversation class
// and add the conversation_id into this to make the app scalable
// but I'm too lazy to do that right now
export default class Message {
  _id?: ObjectId;
  sender_id: ObjectId;
  receiver_id: ObjectId;
  content: string;
  created_at?: Date;

  constructor({ _id, sender_id, receiver_id, content, created_at }: TMessage) {
    this._id = _id || new ObjectId();
    this.sender_id = sender_id;
    this.receiver_id = receiver_id;
    this.content = content;
    this.created_at = created_at || new Date();
  }
}
