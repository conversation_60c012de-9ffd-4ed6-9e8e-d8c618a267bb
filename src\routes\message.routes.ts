import { Router } from "express";
import { getConversationController } from "~/controllers/messages.controllers";
import { accessTokenValidator, verifiedUserValidator } from "~/middlewares/users.middlewares";
import { wrapRequestHandler } from "~/utils/requestHandlers";

const messageRouter = Router();

messageRouter.get(
  "/receiver/:receiverId",
  accessTokenValidator,
  verifiedUserValidator,
  wrapRequestHandler(getConversationController),
);

export default messageRouter;
