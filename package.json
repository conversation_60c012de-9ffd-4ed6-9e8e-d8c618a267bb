{"name": "elon-ma-twitter", "version": "1.0.0", "main": "index.js", "repository": "https://github.com/TranDangKhoi/elon-ma-twitter.git", "author": "TranDangKhoi <<EMAIL>>", "license": "MIT", "scripts": {"dev": "npx nodemon --development", "build": "node scripts/build.js", "start": "node dist/index.js --production", "lint": "eslint .", "lint:fix": "eslint . --fix", "prettier": "prettier --check .", "prettier:fix": "prettier --write ."}, "devDependencies": {"@types/cors": "^2.8.14", "@types/express": "^4.17.17", "@types/express-serve-static-core": "^4.19.5", "@types/formidable": "^3.4.5", "@types/jsonwebtoken": "^9.0.2", "@types/lodash": "^4.14.195", "@types/mime": "^3.0.4", "@types/minimist": "^1.2.5", "@types/node": "^20.3.1", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "eslint": "^8.43.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "nodemon": "^2.0.22", "pino": "^9.6.0", "pino-http": "^10.4.0", "pino-pretty": "^13.0.0", "prettier": "^2.8.8", "rimraf": "^5.0.1", "ts-node": "^10.9.1", "tsc-alias": "^1.8.6", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "dependencies": {"@faker-js/faker": "^8.4.1", "axios": "^1.6.2", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "formidable": "^3.5.1", "jsonwebtoken": "^9.0.0", "lodash": "^4.17.21", "mime": "^3.0.0", "minimist": "^1.2.8", "mongodb": "^5.6.0", "nanoid": "^3.3.7", "sharp": "^0.33.0", "slash": "^5.1.0", "socket.io": "^4.7.5", "zx": "^7.2.3"}, "optionalDependencies": {"bufferutil": "^4.0.8", "utf-8-validate": "^5.0.10"}, "packageManager": "pnpm@9.4.0+sha512.f549b8a52c9d2b8536762f99c0722205efc5af913e77835dbccc3b0b0b2ca9e7dc8022b78062c17291c48e88749c70ce88eb5a74f1fa8c4bf5e18bb46c8bd83a"}