import { Request, Response } from "express";
import { ParamsDictionary } from "express-serve-static-core";
import { TokenPayload } from "~/models/requests/User.requests";
import conversationService from "~/services/conversation.services";

export const getConversationController = async (req: Request<ParamsDictionary, any, any>, res: Response) => {
  const { user_id: sender_id } = req.decoded_access_token as TokenPayload;
  const receiver_id = req.params.receiverId;
  const result = await conversationService.getConversation({
    sender_id,
    receiver_id,
  });
  res.status(200).send({
    message: "Get conversation successfully",
    result,
  });
};
