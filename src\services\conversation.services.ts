import { ObjectId } from "mongodb";
import databaseService from "~/services/database.services";

class ConversationService {
  async getConversation({ sender_id, receiver_id }: { sender_id: string; receiver_id: string }) {
    const conversation = await databaseService.messages
      .find({
        sender_id: new ObjectId(sender_id),
        receiver_id: new ObjectId(receiver_id),
      })
      .toArray();
    return conversation;
  }
}

const conversationService = new ConversationService();
export default conversationService;
