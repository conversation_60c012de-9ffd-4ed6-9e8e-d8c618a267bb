# Elon Ma Twitter

## How I set up the project

- Follow these steps in this repo: [Navigate to the repo](https://github.com/TranDangKhoi/nodejs-starter-kit-2/blob/main/README.md)

## Features

- Authentication

  - Sign up
  - Sign in
  - Sign out
  - Email verification
  - Forgot password
  - Login with Google Account

- Media files upload

  - Upload images
  - Upload videos
  - Upload GIFs
  - Streaming Video (HLS Streaming), i will also create a client demo for this feature

- Tweet

  - Create tweet
  - Delete tweet
  - Retweet, Quote tweet
  - Like tweets
  - Bookmark, Unbookmark
  - Twitter Circle
  - Hashtags
  - ...e.t.c

- Others
  - Real-time chatting
  - Elastic Search
  - Deployment on a VPS
  - Config Nginx, https, domain

## Things i was and gonna be using

- Programming Language: Typescript
- Framework: Express
- Validations: Express Validator
- Database: MongoDB
- Authentication: JWT & OAuth 2.0 (Google)
- File upload: Formidable v3
- Image Processing: Sharp
- Video Streaming: HLS
- Websocket: Socket.io
- TypeODM: MongoDB Driver (I know it's not an ORM, but i don't want to use neither Mongoose nor Prisma)\`
- Deployment: Docker
- 3rd party services (E-mail sending, remote computing, ...): AWS
- API Documentation: Swagger
