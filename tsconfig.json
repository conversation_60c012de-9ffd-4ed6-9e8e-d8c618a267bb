{
  "compilerOptions": {
    "module": "NodeNext", // <PERSON>uy định output module đư<PERSON>c sử dụng
    "moduleResolution": "NodeNext", //
    "target": "ES2020", // Target ouput cho code
    "outDir": "dist", // Đ<PERSON>ờng dẫn output cho thư mục build
    "esModuleInterop": true /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */,
    "strict": true /* Enable all strict type-checking options. */,
    "skipLibCheck": true /* Skip type checking all .d.ts files. */,
    "baseUrl": ".", // Đường dẫn base cho các import
    "paths": {
      // Đ<PERSON>ờng dẫn alias cho các import
      "~/*": ["src/*"]
    }
  },
  "ts-node": {
    "require": ["tsconfig-paths/register"]
  },
  "files": ["src/types.d.ts"], // Các file dùng để defined global type cho dự án
  "include": ["src/**/*"] // Đường dẫn include cho các file cần build
}
